<script>
	import { onDestroy, onMount, tick } from 'svelte';
	import { fade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { tweened } from 'svelte/motion';

	let useY = $state(false);
	const letterSwapIntervalMs = 4500; // 4.5 seconds for i/y swap

	const words = $state(['anime', 'manga', 'light novel', 'visual novel']);
	let currentWordIndex = $state(0);
	let isCurrentWordFullyTyped = $state(false);

	// Mobile detection
	let isMobile = $state(false);

	// Timing constants for typewriter effect (3 seconds total cycle)
	const typingSpeedMs = 70;
	const retractSpeedMs = typingSpeedMs * 0.7;
	const typingStartDelayMs = 50;
	const retractStartDelayMs = 100;
	const wordFullDisplayPauseMs = 1422; // Calculated to make cycle exactly 3 seconds

	let letterIntervalId;
	let nextWordTimeoutId;

	const typedWordContainerWidth = tweened(15, {
		duration: 150,
		easing: quintOut
	});
	let typewriterTargetElement = $state(null);

	function checkMobile() {
		isMobile = window.innerWidth < 768; // md breakpoint
	}

	onMount(() => {
		checkMobile();
		window.addEventListener('resize', checkMobile);

		// Only start animations on desktop
		if (!isMobile) {
			letterIntervalId = setInterval(() => {
				useY = !useY;
			}, letterSwapIntervalMs);
		}
	});

	onDestroy(() => {
		if (typeof window !== 'undefined') {
			window.removeEventListener('resize', checkMobile);
		}
		clearInterval(letterIntervalId);
		clearTimeout(nextWordTimeoutId);
	});

	function scheduleNextWordChange() {
		clearTimeout(nextWordTimeoutId);
		nextWordTimeoutId = setTimeout(() => {
			if (document.hidden) {
				scheduleNextWordChange();
				return;
			}
			startRetractingWord();
		}, wordFullDisplayPauseMs);
	}

	function startRetractingWord() {
		if (!typewriterTargetElement || !typewriterTargetElement.textContent) {
			moveToNextWord();
			return;
		}

		isCurrentWordFullyTyped = false;
		let currentText = typewriterTargetElement.textContent;

		function retractCharacter() {
			if (currentText.length > 0) {
				currentText = currentText.slice(0, -1);
				typewriterTargetElement.textContent = currentText;
				updateTypedWordWidth();
				setTimeout(retractCharacter, retractSpeedMs);
			} else {
				moveToNextWord();
			}
		}

		setTimeout(retractCharacter, retractStartDelayMs);
	}

	function moveToNextWord() {
		currentWordIndex = (currentWordIndex + 1) % words.length;
		isCurrentWordFullyTyped = false;
	}

	async function updateTypedWordWidth() {
		await tick();
		if (typewriterTargetElement) {
			const textContent = typewriterTargetElement.textContent || '';

			const tempMeasureSpan = document.createElement('span');
			Object.assign(tempMeasureSpan.style, {
				fontSize: getComputedStyle(typewriterTargetElement).fontSize,
				fontFamily: getComputedStyle(typewriterTargetElement).fontFamily,
				fontWeight: getComputedStyle(typewriterTargetElement).fontWeight,
				letterSpacing: getComputedStyle(typewriterTargetElement).letterSpacing,
				visibility: 'hidden',
				position: 'absolute',
				whiteSpace: 'nowrap'
			});
			tempMeasureSpan.textContent = textContent || ' ';
			document.body.appendChild(tempMeasureSpan);
			const newWidth = tempMeasureSpan.offsetWidth;
			document.body.removeChild(tempMeasureSpan);

			const extraWidthForCursor = isCurrentWordFullyTyped ? 2 : 10;
			typedWordContainerWidth.set(Math.max(15, newWidth + extraWidthForCursor));
		} else {
			typedWordContainerWidth.set(15);
		}
	}

	function typewriterAction(node, params) {
		// Skip typewriter on mobile
		if (isMobile) {
			return {
				destroy() {}
			};
		}

		let currentDisplayedText = '';
		let charIndex = 0;
		let typingTimeoutId;

		node.textContent = '';
		isCurrentWordFullyTyped = false;
		updateTypedWordWidth();

		function typeCharacter() {
			if (charIndex < params.textToType.length) {
				currentDisplayedText += params.textToType[charIndex];
				node.textContent = currentDisplayedText;
				charIndex++;
				updateTypedWordWidth();
				typingTimeoutId = setTimeout(typeCharacter, typingSpeedMs);
			} else {
				isCurrentWordFullyTyped = true;
				updateTypedWordWidth();
				scheduleNextWordChange();
			}
		}

		setTimeout(typeCharacter, typingStartDelayMs);

		return {
			destroy() {
				clearTimeout(typingTimeoutId);
			}
		};
	}

	const aniThingFirstPart = 'An';
	const aniThingLastPart = 'thing';
	const aniWhereFirstPart = ' an';
	const aniWhereLastPart = 'where.';
</script>

{#if isMobile}
	<!-- Mobile: Static text with emphasis on "weeb" -->
	<h1 class="dynamic-title text-center text-3xl leading-tight font-bold text-slate-50 select-none xs:text-4xl sm:text-5xl md:text-6xl min-h-[4rem] xs:min-h-[5rem] sm:min-h-[6rem] md:min-h-[7rem] flex items-center justify-center">
		<span class="break-words">
			Anything <span class="text-sky-400 font-extrabold">weeb</span>, anywhere.
		</span>
	</h1>
{:else}
	<!-- Desktop: Animated version -->
	<h1 class="dynamic-title text-center text-3xl leading-tight font-bold text-slate-50 select-none xs:text-4xl sm:text-5xl md:text-6xl min-h-[4rem] xs:min-h-[5rem] sm:min-h-[6rem] md:min-h-[7rem] flex items-center justify-center">
		<div class="inline-block">
			<span class="static-text-part">{aniThingFirstPart}</span><span class="letter-container" style="margin: 0 {useY ? '-0.25em' : '-0.40em'} 0 {useY ? '-0.00em' : '-0.10em'};">
				<span class="letter-placeholder"></span>
				{#key useY + 'anithing'}
					<span
						class="letter-swapper"
						in:fade={{ duration: 600, delay: 100, easing: quintOut }}
						out:fade={{ duration: 500, easing: quintOut }}
						>{#if useY}y{:else}i{/if}</span
					>
				{/key}
			</span>
			<span class="static-text-part">{aniThingLastPart}</span>

			<span class="bracketed-word-outer-container text-sky-400 block xs:inline">
				{#key currentWordIndex}
					<span
						class="bracketed-word-inner-container"
						style="width: {$typedWordContainerWidth}px;"
						out:fade={{ duration: 300, easing: quintOut }}
						role="status"
						aria-live="polite"
						aria-atomic="true"
					>
						<span
							bind:this={typewriterTargetElement}
							use:typewriterAction={{ textToType: words[currentWordIndex] }}
							class:typing-done={isCurrentWordFullyTyped}
							class="typewriter-target"
						></span>
					</span>
				{/key}
			</span><span class="comma-separator block xs:inline">,</span>

			<span class="static-text-part">{aniWhereFirstPart}</span><span class="letter-container" style="margin: 0 {useY ? '-0.25em' : '-0.40em'} 0 {useY ? '-0.00em' : '-0.10em'};">
				<span class="letter-placeholder"></span>
				{#key useY + 'aniwhere'}
					<span
						class="letter-swapper"
						in:fade={{ duration: 600, delay: 100, easing: quintOut }}
						out:fade={{ duration: 500, easing: quintOut }}
						>{#if useY}y{:else}i{/if}</span
					>
				{/key}
			</span>
			<span class="static-text-part">{aniWhereLastPart}</span>
		</div>
	</h1>
{/if}

<style>
	.static-text-part,
	.letter-swapper,
	.bracketed-word-outer-container,
	.bracketed-word-inner-container,
	.typewriter-target {
		display: inline-block;
		vertical-align: middle;
		transform-origin: center;
	}

	.letter-swapper {
		width: 1ch;
		text-align: center;
		position: absolute;
		left: 0;
		top: 0;
		transform-origin: bottom center;
	}

	.letter-container {
		position: relative;
		display: inline-block;
		width: 1ch;
		text-align: center;
		vertical-align: middle;
	}

	.letter-placeholder {
		visibility: hidden;
		width: 1ch;
		display: inline-block;
		content: 'y';
	}

	.bracketed-word-outer-container {
		margin-left: 0.1em;
		margin-right: 0.1em;
		position: relative;
		text-align: center;
		vertical-align: middle;
		display: inline-flex;
		align-items: center;
		transform: translateY(1px);
	}

	.bracketed-word-inner-container {
		min-height: 1.5em;
		overflow: hidden;
		text-align: left;
		position: relative;
		display: inline-block;
		vertical-align: middle;
		padding-bottom: 0.2em;
	}

	.typewriter-target {
		white-space: nowrap;
		position: relative;
		display: inline-block;
		line-height: 1.2;
	}

	.dynamic-title {
		margin-top: 0;
		margin-bottom: 0.25em;
		line-height: 1.2;
		display: inline-block;
	}

	.typewriter-target.typing-done::after {
		opacity: 0.7;
	}

	.bracketed-word-inner-container::after {
		content: '▎';
		display: inline-block;
		color: oklch(74.6% 0.16 232.661);
		font-size: 1.2em;
		position: absolute;
		right: -18px;
		top: 45%;
		transform: translateY(-50%) scaleX(1.5);
		opacity: 1;
		animation: typewriter-pulse 1.5s ease-in-out infinite;
		text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
		font-weight: bold;
		z-index: 10;
		letter-spacing: 0;
	}

	.comma-separator {
		display: inline-block;
		position: relative;
		vertical-align: baseline;
		transform: translateY(10px);
		margin: 0 0.05em;
	}

@media (max-width: 480px) {
	.bracketed-word-inner-container {
		min-width: 80px;
		margin: 0.2em auto;
	}

	.bracketed-word-inner-container::after {
		right: -12px;
		font-size: 1em;
	}

	.comma-separator {
		margin: 0.1em 0;
	}
}

	@keyframes blink {
		0%,
		100% {
			opacity: 0.7;
		}
		50% {
			opacity: 0.2;
		}
	}

	@keyframes typewriter-pulse {
		0%,
		100% {
			opacity: 1;
			transform: translateY(-50%) scaleX(1.5);
			text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
		}
		50% {
			opacity: 0.4;
			transform: translateY(-50%) scaleX(1.5);
			text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
		}
	}
</style>
